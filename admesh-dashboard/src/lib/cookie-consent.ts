/**
 * Comprehensive Cookie Consent Management Utility
 * Handles localStorage/cookie fallback, expiration, and GDPR/CCPA compliance
 */

export interface CookiePreferences {
  necessary: boolean;
  analytics: boolean;
  marketing: boolean;
  functional: boolean;
}

export interface ConsentData {
  preferences: CookiePreferences;
  timestamp: number;
  version: string; // For tracking consent version changes
  expiresAt: number;
  userAgent?: string; // For tracking consent across different browsers
}

export interface ConsentStatus {
  hasConsent: boolean;
  isExpired: boolean;
  preferences: CookiePreferences | null;
  shouldShowBanner: boolean;
  consentData: ConsentData | null;
}

// Constants
const CONSENT_STORAGE_KEY = 'admesh-cookie-consent';
const CONSENT_PREFERENCES_KEY = 'admesh-cookie-preferences';
const CONSENT_DATA_KEY = 'admesh-consent-data';
const CONSENT_VERSION = '1.0.0';
const CONSENT_EXPIRY_DAYS = 365; // 1 year
const FALLBACK_COOKIE_NAME = 'admesh_consent';

/**
 * Set a fallback cookie when localStorage is not available
 */
function setCookie(name: string, value: string, days: number): void {
  if (typeof window === 'undefined') return; // Server-side rendering check
  const expires = new Date();
  expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
  document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/;SameSite=Lax`;
}

/**
 * Get a fallback cookie when localStorage is not available
 */
function getCookie(name: string): string | null {
  if (typeof window === 'undefined') return null; // Server-side rendering check
  const nameEQ = name + "=";
  const ca = document.cookie.split(';');
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
  }
  return null;
}

/**
 * Check if localStorage is available
 */
function isLocalStorageAvailable(): boolean {
  if (typeof window === 'undefined') return false; // Server-side rendering check
  try {
    const test = '__localStorage_test__';
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch {
    return false;
  }
}

/**
 * Store consent data with localStorage/cookie fallback
 */
function storeConsentData(data: ConsentData): void {
  const serializedData = JSON.stringify(data);
  
  if (isLocalStorageAvailable()) {
    try {
      localStorage.setItem(CONSENT_DATA_KEY, serializedData);
      localStorage.setItem(CONSENT_STORAGE_KEY, 'true');
      localStorage.setItem(CONSENT_PREFERENCES_KEY, JSON.stringify(data.preferences));
    } catch (error) {
      console.warn('Failed to store consent in localStorage, falling back to cookies:', error);
      setCookie(FALLBACK_COOKIE_NAME, serializedData, CONSENT_EXPIRY_DAYS);
    }
  } else {
    setCookie(FALLBACK_COOKIE_NAME, serializedData, CONSENT_EXPIRY_DAYS);
  }
}

/**
 * Retrieve consent data with localStorage/cookie fallback
 */
function getConsentData(): ConsentData | null {
  let serializedData: string | null = null;
  
  if (isLocalStorageAvailable()) {
    try {
      serializedData = localStorage.getItem(CONSENT_DATA_KEY);
    } catch (error) {
      console.warn('Failed to read from localStorage, falling back to cookies:', error);
    }
  }
  
  // Fallback to cookie if localStorage failed or is not available
  if (!serializedData) {
    serializedData = getCookie(FALLBACK_COOKIE_NAME);
  }
  
  if (!serializedData) {
    return null;
  }
  
  try {
    return JSON.parse(serializedData) as ConsentData;
  } catch (error) {
    console.warn('Failed to parse consent data:', error);
    return null;
  }
}

/**
 * Create consent data object
 */
function createConsentData(preferences: CookiePreferences): ConsentData {
  const now = Date.now();
  return {
    preferences,
    timestamp: now,
    version: CONSENT_VERSION,
    expiresAt: now + (CONSENT_EXPIRY_DAYS * 24 * 60 * 60 * 1000),
    userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'server'
  };
}

/**
 * Check if consent has expired
 */
function isConsentExpired(consentData: ConsentData): boolean {
  return Date.now() > consentData.expiresAt;
}

/**
 * Check if consent version is outdated
 */
function isConsentVersionOutdated(consentData: ConsentData): boolean {
  return consentData.version !== CONSENT_VERSION;
}

/**
 * Get comprehensive consent status
 */
export function getConsentStatus(): ConsentStatus {
  const consentData = getConsentData();
  
  if (!consentData) {
    return {
      hasConsent: false,
      isExpired: false,
      preferences: null,
      shouldShowBanner: true,
      consentData: null
    };
  }
  
  const isExpired = isConsentExpired(consentData);
  const isOutdated = isConsentVersionOutdated(consentData);
  const shouldShowBanner = isExpired || isOutdated;
  
  return {
    hasConsent: !shouldShowBanner,
    isExpired,
    preferences: consentData.preferences,
    shouldShowBanner,
    consentData
  };
}

/**
 * Save user consent preferences
 */
export function saveConsentPreferences(preferences: CookiePreferences): void {
  const consentData = createConsentData(preferences);
  storeConsentData(consentData);
  
  // Apply preferences immediately
  applyConsentPreferences(preferences);
}

/**
 * Get default preferences (only necessary cookies)
 */
export function getDefaultPreferences(): CookiePreferences {
  return {
    necessary: true,
    analytics: false,
    marketing: false,
    functional: false
  };
}

/**
 * Get all-accepted preferences
 */
export function getAllAcceptedPreferences(): CookiePreferences {
  return {
    necessary: true,
    analytics: true,
    marketing: true,
    functional: true
  };
}

/**
 * Clear all consent data
 */
export function clearConsentData(): void {
  if (isLocalStorageAvailable()) {
    try {
      localStorage.removeItem(CONSENT_DATA_KEY);
      localStorage.removeItem(CONSENT_STORAGE_KEY);
      localStorage.removeItem(CONSENT_PREFERENCES_KEY);
    } catch (error) {
      console.warn('Failed to clear localStorage consent data:', error);
    }
  }
  
  // Clear fallback cookie
  setCookie(FALLBACK_COOKIE_NAME, '', -1);
}

/**
 * Apply consent preferences (integrate with actual cookie/tracking systems)
 * GDPR/CCPA compliant implementation
 */
export function applyConsentPreferences(preferences: CookiePreferences): void {
  // Update Google Tag Manager consent state (if GTM is used)
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('consent', 'update', {
      analytics_storage: preferences.analytics ? 'granted' : 'denied',
      ad_storage: preferences.marketing ? 'granted' : 'denied',
      functionality_storage: preferences.functional ? 'granted' : 'denied',
      personalization_storage: preferences.functional ? 'granted' : 'denied',
      security_storage: 'granted' // Always granted for necessary cookies
    });
  }

  // Analytics cookies (Google Analytics, Vercel Analytics, etc.)
  if (preferences.analytics) {
    console.log('Analytics cookies enabled');
    // Enable analytics tracking
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('config', 'GA_MEASUREMENT_ID', {
        anonymize_ip: true, // GDPR compliance
        allow_google_signals: false // Disable advertising features for privacy
      });
    }
  } else {
    console.log('Analytics cookies disabled');
    // Disable analytics tracking and clear existing analytics cookies
    clearAnalyticsCookies();
  }

  // Marketing cookies (advertising, retargeting, etc.)
  if (preferences.marketing) {
    console.log('Marketing cookies enabled');
    // Enable marketing/advertising cookies
    enableMarketingCookies();
  } else {
    console.log('Marketing cookies disabled');
    // Disable marketing cookies and clear existing ones
    clearMarketingCookies();
  }

  // Functional cookies (user preferences, language settings, etc.)
  if (preferences.functional) {
    console.log('Functional cookies enabled');
    // Enable functional cookies
    enableFunctionalCookies();
  } else {
    console.log('Functional cookies disabled');
    // Disable functional cookies and clear existing ones
    clearFunctionalCookies();
  }

  // Necessary cookies are always enabled (GDPR/CCPA compliant)
  console.log('Necessary cookies enabled (always) - required for website functionality');
}

/**
 * Clear analytics cookies for GDPR compliance
 */
function clearAnalyticsCookies(): void {
  if (typeof window === 'undefined') return; // Server-side rendering check
  const analyticsCookies = ['_ga', '_ga_', '_gid', '_gat', '_gtag', '__utma', '__utmb', '__utmc', '__utmt', '__utmz'];
  analyticsCookies.forEach(cookieName => {
    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${window.location.hostname}`;
    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
  });
}

/**
 * Clear marketing cookies for GDPR compliance
 */
function clearMarketingCookies(): void {
  if (typeof window === 'undefined') return; // Server-side rendering check
  const marketingCookies = ['_fbp', '_fbc', 'fr', 'tr', 'ads', 'IDE', 'DSID', 'test_cookie'];
  marketingCookies.forEach(cookieName => {
    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${window.location.hostname}`;
    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
  });
}

/**
 * Clear functional cookies for GDPR compliance
 */
function clearFunctionalCookies(): void {
  if (typeof window === 'undefined') return; // Server-side rendering check
  const functionalCookies = ['lang', 'theme', 'preferences', 'session_id'];
  functionalCookies.forEach(cookieName => {
    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${window.location.hostname}`;
    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
  });
}

/**
 * Enable marketing cookies
 */
function enableMarketingCookies(): void {
  // Re-initialize marketing scripts if needed
  // Example: Facebook Pixel, Google Ads, etc.
}

/**
 * Enable functional cookies
 */
function enableFunctionalCookies(): void {
  // Re-initialize functional features if needed
  // Example: User preferences, language settings, etc.
}

/**
 * Check if user has rejected all non-necessary cookies
 */
export function hasRejectedAllCookies(): boolean {
  const status = getConsentStatus();
  if (!status.preferences) return false;
  
  return !status.preferences.analytics && 
         !status.preferences.marketing && 
         !status.preferences.functional;
}

/**
 * Get user's geographic location for GDPR/CCPA compliance
 */
export function getUserRegion(): Promise<'EU' | 'CA' | 'US' | 'OTHER'> {
  return new Promise((resolve) => {
    if (typeof window === 'undefined') {
      resolve('OTHER'); // Default for server-side rendering
      return;
    }

    // Try to get timezone-based region detection
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    if (timezone.includes('Europe/')) {
      resolve('EU');
    } else if (timezone.includes('America/') && (timezone.includes('Toronto') || timezone.includes('Vancouver'))) {
      resolve('CA');
    } else if (timezone.includes('America/')) {
      resolve('US');
    } else {
      resolve('OTHER');
    }
  });
}

/**
 * Check if user is in a region requiring explicit consent
 */
export async function requiresExplicitConsent(): Promise<boolean> {
  const region = await getUserRegion();
  return region === 'EU' || region === 'CA'; // GDPR and CCPA regions
}

/**
 * Get consent banner text based on user region
 */
export async function getConsentBannerText(): Promise<{
  title: string;
  description: string;
  acceptText: string;
  declineText: string;
  customizeText: string;
}> {
  const region = await getUserRegion();

  switch (region) {
    case 'EU':
      return {
        title: 'We use cookies',
        description: 'We use cookies to improve your experience and analyze site usage. You can manage your preferences or withdraw consent at any time. See our Privacy Policy for more details.',
        acceptText: 'Accept All',
        declineText: 'Reject All',
        customizeText: 'Manage Preferences'
      };
    case 'CA':
      return {
        title: 'Privacy Notice',
        description: 'We use cookies and similar technologies. You have the right to know what personal information we collect and how we use it. Manage your preferences below.',
        acceptText: 'Accept All',
        declineText: 'Decline',
        customizeText: 'Privacy Preferences'
      };
    default:
      return {
        title: 'We use cookies',
        description: 'We use cookies to improve your experience and analyze site usage.',
        acceptText: 'Accept All',
        declineText: 'Decline',
        customizeText: 'Customize'
      };
  }
}

/**
 * Log consent action for compliance audit trail
 */
export function logConsentAction(action: 'accept' | 'decline' | 'customize', preferences: CookiePreferences): void {
  if (typeof window === 'undefined') return; // Server-side rendering check

  const logEntry = {
    timestamp: new Date().toISOString(),
    action,
    preferences,
    userAgent: navigator.userAgent,
    url: window.location.href,
    consentVersion: CONSENT_VERSION
  };

  // Store in localStorage for audit purposes (in production, send to server)
  const auditLog = JSON.parse(localStorage.getItem('admesh-consent-audit') || '[]');
  auditLog.push(logEntry);

  // Keep only last 50 entries to prevent storage bloat
  if (auditLog.length > 50) {
    auditLog.splice(0, auditLog.length - 50);
  }

  localStorage.setItem('admesh-consent-audit', JSON.stringify(auditLog));

  console.log('Consent action logged:', logEntry);
}

/**
 * Initialize consent system on page load with GDPR/CCPA compliance
 */
export function initializeConsentSystem(): ConsentStatus {
  const status = getConsentStatus();

  // Apply existing preferences if available
  if (status.preferences && status.hasConsent) {
    applyConsentPreferences(status.preferences);
  } else {
    // Initialize with minimal consent (necessary cookies only) until user decides
    applyConsentPreferences(getDefaultPreferences());
  }

  return status;
}

/**
 * Enhanced save consent preferences with compliance logging
 */
export function saveConsentPreferencesWithLogging(preferences: CookiePreferences, action: 'accept' | 'decline' | 'customize'): void {
  // Save preferences
  saveConsentPreferences(preferences);

  // Log action for compliance
  logConsentAction(action, preferences);
}

/**
 * Get data processing purposes for transparency
 */
export function getDataProcessingPurposes(): {
  necessary: string[];
  analytics: string[];
  marketing: string[];
  functional: string[];
} {
  return {
    necessary: [
      'Website functionality',
      'Security and fraud prevention',
      'Load balancing',
      'Session management'
    ],
    analytics: [
      'Website usage statistics',
      'Performance monitoring',
      'Error tracking',
      'User behavior analysis (anonymized)'
    ],
    marketing: [
      'Personalized advertising',
      'Retargeting campaigns',
      'Social media integration',
      'Marketing effectiveness measurement'
    ],
    functional: [
      'User preferences storage',
      'Language settings',
      'Theme preferences',
      'Enhanced user experience features'
    ]
  };
}
