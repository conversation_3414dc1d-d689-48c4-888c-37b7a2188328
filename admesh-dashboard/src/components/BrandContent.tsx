"use client";

import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import MobileNav from "@/components/MobileNav";
import { useState, useEffect, Suspense } from "react";
import { useInView } from "react-intersection-observer";
import { useAuth } from "@/hooks/use-auth";
import {
  BrainCircuit,
  CreditCard,
  Search,
  Target,
  TrendingUp,
  Building,
  BarChart,
  Zap,
  SearchCheck
} from "lucide-react";
import { useSearchParams } from "next/navigation";
import FreeSEOCheckSection from "./FreeSEOCheckSection";

type Role = "user" | "brand" | "agent" | null;

interface BrandContentProps {
  onRoleChange: (role: Role) => void;
}

// SearchParamsHandler component to handle search params logic
function SearchParamsHandler() {
  const searchParams = useSearchParams();

  useEffect(() => {
    // Get tracking parameters from URL
    const clickId = searchParams.get('utm_click_id') || searchParams.get('clickId');
    const source = searchParams.get('utm_source') || searchParams.get('source');
    const target = searchParams.get('utm_target') || searchParams.get('target');

    // Log all URL parameters for debugging
    console.log('URL parameters:', Object.fromEntries([...searchParams.entries()]));

    // Check if we have the necessary tracking parameters
    if (clickId) {
      // Be more lenient with source check - either 'admesh' or not specified
      if (!source || source.toLowerCase() === 'admesh') {
        // Use target if available, otherwise use a default value
        const targetValue = target || 'default';
        const key = `admesh_referral_${targetValue}`;
        const trackingData = {
          clickId,
          source: source || 'admesh',
          target: targetValue,
          timestamp: Date.now(),
          test: searchParams.get('test') === 'true'
        };

        // Store in localStorage
        localStorage.setItem(key, JSON.stringify(trackingData));
        console.log('AdMesh tracking parameters stored:', trackingData);

        // Also store in sessionStorage as a backup
        sessionStorage.setItem(key, JSON.stringify(trackingData));

        // Also store in a generic key for easier access
        localStorage.setItem('admesh_last_referral', JSON.stringify(trackingData));

        // Show a notification for test mode
        if (trackingData.test) {
          console.log('Test tracking parameters detected and stored');
          // We'll just use console.log for now since we don't have direct access to toast
        }
      }
    }
  }, [searchParams]);

  return null;
}

export default function BrandContent({ /* onRoleChange */ }: BrandContentProps) {
  const [, setScrollProgress] = useState(0);
  const [activeSection, setActiveSection] = useState("hero");
  const { user } = useAuth();
  // Track scroll position for animations and nav highlighting
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const docHeight = document.body.offsetHeight - window.innerHeight;
      const scrollPercent = (scrollTop / docHeight) * 100;
      setScrollProgress(scrollPercent);

      // Update active section based on scroll position
      const sections = [
        "hero",
        "journey",
        "benefits",
        "cta",
      ];
      sections.forEach((section) => {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          if (
            rect.top <= window.innerHeight / 2 &&
            rect.bottom >= window.innerHeight / 2
          ) {
            setActiveSection(section);
          }
        }
      });
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // InView hooks for each section
  const { ref: heroRef, inView: heroInView } = useInView({ triggerOnce: true });
  const { ref: journeyRef, inView: journeyInView } = useInView({
    triggerOnce: true,
  });
  const { ref: benefitsRef, inView: benefitsInView } = useInView({
    triggerOnce: true,
  });
  const journeySteps = [
    {
      icon: <BrainCircuit className="w-10 h-10 text-black" />,
      title: "Publish Your Offers",
      description:
        "Upload your products to the AdMesh network with structured metadata that makes them discoverable by AI agents.",
    },
    {
      icon: <SearchCheck className="w-10 h-10 text-black" />,
      title: "Get Matched by Agents",
      description:
        "AI agents dynamically pull your offers based on real-time user queries and preferences.",
    },
    {
      icon: <CreditCard className="w-10 h-10 text-black" />,
      title: "Pay Only for Results",
      description:
        "Only pay when users take verifiable actions — clicks, signups, or purchases. Maximize your ROI.",
    },
  ];

  const benefits = [
    {
      icon: <Search className="w-10 h-10 text-black" />,
      title: "Native Discovery",
      description:
        "Be discovered naturally through AI agent conversations, not intrusive ads.",
    },
    {
      icon: <Target className="w-10 h-10 text-black" />,
      title: "Intent-Verified Traffic",
      description:
        "Reach users exactly when they are searching, comparing, or deciding.",
    },
    {
      icon: <TrendingUp className="w-10 h-10 text-black" />,
      title: "Performance-Only Billing",
      description:
        "No impressions. No wasted clicks. Only pay for measurable results.",
    },
  ];


  const scrollToSection = (sectionId: string) => {
    const section = document.getElementById(sectionId);
    if (section) {
      section.scrollIntoView({ behavior: "smooth" });
    }
  };

  const handleGetStarted = () => {
    if (user) {
      window.location.href = "/dashboard";
    } else {
      window.location.href = "/auth/signin?role=brand";
    }
  };

  return (
    <div className="w-full">
      {/* Suspense boundary for search params */}
      <Suspense fallback={null}>
        <SearchParamsHandler />
      </Suspense>

      {/* Mobile-friendly Navigation */}
      <MobileNav
        items={[
          { label: "Home", sectionId: "hero" },
          { label: "How It Works", sectionId: "how-it-works" },
          { label: "Benefits", sectionId: "benefits" },
          { label: "Free GEO Check", sectionId: "free-seo-check" }
        ]}
        activeSection={activeSection}
        scrollToSection={scrollToSection}
        onGetStarted={handleGetStarted}
        themeColor="black"
        user={user}
      />

      {/* Hero Section */}
      <section
        id="hero"
        ref={heroRef}
        className="w-full min-h-screen flex items-center justify-center bg-white pt-20 pb-16 px-4"
      >
        <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="text-center lg:text-left">
            <motion.span
              className="inline-block mb-3 px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm font-medium"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: heroInView ? 1 : 0, y: heroInView ? 0 : 20 }}
              transition={{ duration: 0.3 }}
            >
              Join Waitlist
            </motion.span>

            <motion.h1
              className="text-4xl sm:text-5xl md:text-6xl font-extrabold tracking-tight leading-tight mb-6 text-black"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: heroInView ? 1 : 0, y: heroInView ? 0 : 20 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              Reach Users the Moment They Search Through AI Agents
            </motion.h1>

            <motion.p
              className="text-lg sm:text-xl text-gray-600 max-w-xl mx-auto lg:mx-0 mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: heroInView ? 1 : 0, y: heroInView ? 0 : 20 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              AdMesh places your products directly inside real conversations
              happening across AI tools, right when users are searching,
              comparing, and ready to act.
            </motion.p>

            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: heroInView ? 1 : 0, y: heroInView ? 0 : 20 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <Button
                size="lg"
                className="bg-black hover:bg-gray-800 text-white font-medium shadow-md hover:shadow-lg transition-all"
                onClick={handleGetStarted}
              >
                {user ? "Go to Dashboard" : "Get Started"}
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="border-gray-300 hover:bg-gray-50"
                onClick={() => scrollToSection("journey")}
              >
                Learn More
              </Button>
            </motion.div>
          </div>

          {/* Mobile-only message */}


          <motion.div
            className="relative rounded-xl shadow-xl hidden lg:block"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{
              opacity: heroInView ? 1 : 0,
              scale: heroInView ? 1 : 0.95,
            }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <div className="bg-white rounded-xl p-6 h-full  border border-gray-200">
              {/* Header */}
              <div className="mb-4 flex justify-between items-center">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-full bg-black flex items-center justify-center shadow-md">
                    <Building className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold">Brand Dashboard</h3>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-500">
                        Your CPA Performance
                      </span>
                      <div className="w-2 h-2 rounded-full bg-green-500 animate-pulse"></div>
                      <span className="text-xs text-green-600 font-medium">
                        Live
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="bg-gray-100 hover:bg-gray-200 transition-colors p-2 rounded-full cursor-pointer">
                    <BarChart className="w-5 h-5 text-gray-700" />
                  </div>
                  <div className="bg-black hover:bg-gray-800 transition-colors p-2 rounded-full cursor-pointer">
                    <Zap className="w-5 h-5 text-white" />
                  </div>
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-2 ">
                <div className="bg-gray-50 hover:bg-gray-100 transition-all rounded-xl p-4 border border-gray-200 hover:shadow-md">
                  <div className="flex items-center justify-between mb-2">
                    <p className="text-sm text-gray-500">Total Discoveries</p>
                    <div className="w-8 h-8 rounded-full bg-black/10 flex items-center justify-center">
                      <Search className="w-4 h-4 text-black" />
                    </div>
                  </div>
                  <p className="text-2xl font-bold">12,400</p>
                  <div className="mt-2 flex items-center text-xs text-green-600">
                    <span className="mr-1">↑</span>
                    <span>8.2% vs. last week</span>
                  </div>
                </div>

                <div className="bg-gray-50 hover:bg-gray-100 transition-all rounded-xl p-4 border border-gray-200 hover:shadow-md">
                  <div className="flex items-center justify-between mb-2">
                    <p className="text-sm text-gray-500">Total Clicks</p>
                    <div className="w-8 h-8 rounded-full bg-black/10 flex items-center justify-center">
                      <Target className="w-4 h-4 text-black" />
                    </div>
                  </div>
                  <p className="text-2xl font-bold">1,580</p>
                  <div className="mt-2 flex items-center text-xs text-green-600">
                    <span className="mr-1">↑</span>
                    <span>12.5% vs. last week</span>
                  </div>
                </div>

                <div className="bg-gray-50 hover:bg-gray-100 transition-all rounded-xl p-4 border border-gray-200 hover:shadow-md">
                  <div className="flex items-center justify-between mb-2">
                    <p className="text-sm text-gray-500">Total Conversions</p>
                    <div className="w-8 h-8 rounded-full bg-black/10 flex items-center justify-center">
                      <CreditCard className="w-4 h-4 text-black" />
                    </div>
                  </div>
                  <p className="text-2xl font-bold">317</p>
                  <div className="mt-2 flex items-center text-xs text-green-600">
                    <span className="mr-1">↑</span>
                    <span>5.3% vs. last week</span>
                  </div>
                </div>

                <div className="bg-gray-50 hover:bg-gray-100 transition-all rounded-xl p-4 border border-gray-200 hover:shadow-md">
                  <div className="flex items-center justify-between mb-2">
                    <p className="text-sm text-gray-500">Total Spend</p>
                    <div className="w-8 h-8 rounded-full bg-black/10 flex items-center justify-center">
                      <TrendingUp className="w-4 h-4 text-black" />
                    </div>
                  </div>
                  <p className="text-2xl font-bold">$9,510</p>
                  <div className="mt-2 flex items-center text-xs text-green-600">
                    <span className="mr-1">↑</span>
                    <span>3.7% vs. last week</span>
                  </div>
                </div>
              </div>

              {/* Offer Performance */}
              <div className="pt-2 border-t border-gray-200">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-sm font-medium">Offer Performance</h4>
                  <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-md">
                    Last 30 days
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm text-gray-700">
                        Click-Through Rate (CTR)
                      </span>
                      <span className="text-sm font-semibold">12.7%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1.5 overflow-hidden">
                      <div
                        className="bg-black h-1.5 rounded-full"
                        style={{ width: "60%" }}
                      ></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm text-gray-700">
                        Conversion Rate (CVR)
                      </span>
                      <span className="text-sm font-semibold">20.1%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1.5 overflow-hidden">
                      <div
                        className="bg-black h-1.5 rounded-full"
                        style={{ width: "75%" }}
                      ></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm text-gray-700">
                        Avg CPA (Cost Per Acquisition)
                      </span>
                      <span className="text-sm font-semibold">$10</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1.5 overflow-hidden">
                      <div
                        className="bg-black h-1.5 rounded-full"
                        style={{ width: "45%" }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>


      </section>

      {/* Journey Section */}
      <section id="journey" ref={journeyRef} className="w-full py-20 bg-white">
        <div className="max-w-6xl mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{
              opacity: journeyInView ? 1 : 0,
              y: journeyInView ? 0 : 30,
            }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <span className="text-sm font-medium text-gray-600 block mb-3">
              HOW IT WORKS
            </span>
            <h2 className="text-3xl md:text-4xl font-bold text-black mb-6">
              AdMesh Connects Your Brand to AI Agents
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Our platform integrates your products directly into the AI agent
              ecosystem, allowing seamless discovery when users are looking for
              solutions.
            </p>
          </motion.div>

          <div className="relative">
            {/* Connection Line */}
            <div className="hidden lg:block absolute top-1/2 left-0 right-0 h-0.5 bg-gray-200 transform -translate-y-1/2 z-0"></div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12 relative z-10">
              {journeySteps.map((step, index) => (
                <motion.div
                  key={index}
                  className="bg-white rounded-xl p-8 shadow-lg border border-gray-100 relative"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{
                    opacity: journeyInView ? 1 : 0,
                    y: journeyInView ? 0 : 20,
                  }}
                  transition={{ duration: 0.5, delay: 0.15 * index }}
                >
                  <div className="w-14 h-14 rounded-full bg-gray-100 flex items-center justify-center mb-6">
                    {step.icon}
                  </div>

                  <span className="absolute top-8 right-8 text-5xl font-bold text-gray-100 select-none">
                    {index + 1}
                  </span>

                  <h3 className="text-xl font-semibold mb-3">{step.title}</h3>
                  <p className="text-gray-600">{step.description}</p>
                </motion.div>
              ))}
            </div>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{
              opacity: journeyInView ? 1 : 0,
              y: journeyInView ? 0 : 20,
            }}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="text-center mt-12"
          >
            <Button
              size="lg"
              className="bg-black hover:bg-gray-800 text-white"
              onClick={handleGetStarted}
            >
              {user ? "Go to Dashboard" : "Get Started"}
            </Button>
          </motion.div>
        </div>
      </section>

      {/* Benefits Section */}
      <section id="benefits" ref={benefitsRef} className="w-full py-20 bg-gray-50">
  <div className="max-w-6xl mx-auto px-4">
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{
        opacity: benefitsInView ? 1 : 0,
        y: benefitsInView ? 0 : 30,
      }}
      transition={{ duration: 0.6 }}
      className="text-center mb-16"
    >
      <span className="text-sm font-medium text-gray-600 block mb-3">
        WHY BRANDS CHOOSE ADMESH
      </span>
      <h2 className="text-3xl md:text-4xl font-bold text-black mb-6">
        Move Beyond Ads — Get Discovered at the Moment of Intent
      </h2>
      <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
        AdMesh puts your products into real conversations inside AI agents —
        reaching users when they are actively searching, comparing, and ready to decide.
      </p>


    </motion.div>

    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
      {benefits.map((benefit, index) => (
        <motion.div
          key={index}
          className="bg-white rounded-xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow"
          initial={{ opacity: 0, y: 20 }}
          animate={{
            opacity: benefitsInView ? 1 : 0,
            y: benefitsInView ? 0 : 20,
          }}
          transition={{ duration: 0.5, delay: 0.15 * index }}
        >
          <div className="w-14 h-14 rounded-full bg-gray-100 flex items-center justify-center mb-6">
            {benefit.icon}
          </div>
          <h3 className="text-xl font-semibold mb-3">{benefit.title}</h3>
          <p className="text-gray-600">{benefit.description}</p>
        </motion.div>
      ))}
    </div>
    <div className="flex justify-center mt-8">
      <Button
        size="lg"
        className="bg-black hover:bg-gray-800 text-white font-medium shadow-md hover:shadow-lg transition-all hover:-translate-y-1 duration-300"
        onClick={handleGetStarted}
      >
        {user ? "Go to Dashboard" : "Get Started"}
      </Button>
    </div>

  </div>
</section>




      {/* Free GEO Check Section */}
      <FreeSEOCheckSection />

      {/* CTA Section */}
      <section id="cta" className="w-full py-20 bg-black text-white">
  <div className="max-w-6xl mx-auto px-4 text-center">
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{
        opacity: benefitsInView ? 1 : 0,
        y: benefitsInView ? 0 : 20,
      }}
      transition={{ duration: 0.5, delay: 0.3 }}
    >
      <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white">
        Ready to Get Discovered at the Moment of Intent?
      </h2>
      <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
        Bring your offers into real AI-driven conversations. No clicks. No guesswork. Just pure, action-ready discovery.
      </p>

      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <Button
          size="lg"
          className="bg-white text-black hover:bg-gray-100 font-medium"
          onClick={handleGetStarted}
        >
          {user ? "Go to Dashboard" : "Get Started"}
        </Button>
      </div>
    </motion.div>
  </div>
</section>




    </div>
  );
}
