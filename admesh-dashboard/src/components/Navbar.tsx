"use client";

import Link from "next/link";
import { useEffect, useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Menu, X } from "lucide-react";
import { onAuthStateChanged, User } from "firebase/auth";
import { auth } from "@/lib/firebase";

import Image from "next/image";


interface NavItem {
  label: string;
  href: string;
  isScroll?: boolean;
}

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [, setAuthOpen] = useState(false);

  const router = useRouter();
  const pathname = usePathname();

  const hideNav = pathname.startsWith("/auth");

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  const handleAuthRedirect = () => {
    let role = "";
    if (pathname.startsWith("/brands")) role = "brand";
    else if (pathname.startsWith("/agents")) role = "agent";
    else if (pathname.startsWith("/users")) role = "user";
    if (role) router.push(`/auth/signin?role=${role}`);
    else router.push("/auth/signin");
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      setUser(currentUser);
    });
    return () => unsubscribe();
  }, []);

  useEffect(() => {
    const onScroll = () => setScrolled(window.scrollY > 10);
    window.addEventListener("scroll", onScroll);
    return () => window.removeEventListener("scroll", onScroll);
  }, []);

  // Define all possible nav items
  const allNavItems: NavItem[] = [
    { label: "Brands", href: "/brands" },
    { label: "Agents", href: "/agents" },
    { label: "Users", href: "/users" },
    { label: "Free GEO Check", href: "#free-geo-check", isScroll: true },
  ];

  // Filter nav items based on current path
  const navItems: NavItem[] = allNavItems.filter(item => {
    // Show "Free GEO Check" only on root, brands, and agents paths
    if (item.label === "Free GEO Check") {
      return pathname === "/" || 
             pathname.startsWith("/brands") || 
             pathname.startsWith("/agents");
    }
    return true;
  });

  if (hideNav) {
    return null;
  }

  return (
    <header
      className={`fixed top-0 z-50 w-full transition-all duration-300 ${
        scrolled
          ? "bg-background/80 backdrop-blur-md border-b border-border shadow-sm"
          : "bg-transparent border-transparent"
      }`}
    >
      <div className="container mx-auto flex h-16 items-center justify-between px-4 sm:px-6">
        {/* Logo */}
        <Link href="/" className="flex items-center">
          <Image src="/logo.svg" alt="AdMesh Logo" width={48} height={48} />
          <span className="text-lg font-bold tracking-tight font-inter text-foreground">AdMesh</span>
        </Link>

        {/* Desktop Nav */}
        <nav className="hidden md:flex items-center space-x-8">
          {navItems.map((link) =>
            link.isScroll ? (
              <a
                key={link.href}
                href={link.href}
                className="text-sm font-medium hover:text-primary transition-colors cursor-pointer"
                onClick={(e) => {
                  e.preventDefault();
                  const parts = link.href.split('#');
                  const path = parts[0];
                  const sectionId = parts[1];
                  
                  if (pathname === path || (path === '/' && pathname === '') ||
                      (path === '/brands' && pathname === '/brands') ||
                      (path === '/users' && pathname === '/users')) {
                    const section = document.getElementById(sectionId);
                    if (section) {
                      section.scrollIntoView({ behavior: 'smooth' });
                    }
                  } else {
                    // Otherwise, navigate to the path with the hash
                    window.location.href = link.href;
                  }
                }}
              >
                {link.label}
              </a>
            ) : (
              <Link
                key={link.href}
                href={link.href}
                className="text-sm font-medium hover:text-primary transition-colors"
              >
                {link.label}
              </Link>
            )
          )}
        </nav>

        {/* Mobile menu button */}
        <div className="flex md:hidden">
          <button
            onClick={toggleMenu}
            className="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"
            aria-expanded="false"
          >
            <span className="sr-only">Open main menu</span>
            {isMenuOpen ? (
              <X className="block h-6 w-6" aria-hidden="true" />
            ) : (
              <Menu className="block h-6 w-6" aria-hidden="true" />
            )}
          </button>
        </div>

        {/* Right Side CTA */}
        <div className="hidden md:flex items-center gap-4">
          {user ? (
            <Link href="/dashboard">
              <Button
                variant="default"
                className="rounded-md bg-primary text-primary-foreground hover:bg-primary/90 transition"
              >
                Dashboard
              </Button>
            </Link>
          ) : (
            <>
              <Button
                variant="ghost"
                className="text-foreground hover:bg-transparent hover:underline"
                onClick={handleAuthRedirect}
              >
                Sign In
              </Button>
              <Button
                className="bg-primary text-primary-foreground hover:bg-primary/90"
                onClick={handleAuthRedirect}
              >
                Get Started
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-background border-t border-border">
          <div className="px-2 pt-2 pb-3 space-y-1">
            {navItems.map((item) => (
              <a
                key={item.href}
                href={item.href}
                className="block px-3 py-2 rounded-md text-base font-medium text-foreground hover:bg-accent hover:text-accent-foreground"
                onClick={() => setIsMenuOpen(false)}
              >
                {item.label}
              </a>
            ))}
            <div className="pt-4 pb-3 border-t border-border mt-4">
              {user ? (
                <Link
                  href="/dashboard"
                  className="block w-full text-left px-4 py-2 text-base font-medium text-foreground hover:bg-accent hover:text-accent-foreground rounded-md"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Go to Dashboard
                </Link>
              ) : (
                <div className="space-y-2">
                  <button
                    onClick={() => {
                      setAuthOpen(true);
                      setIsMenuOpen(false);
                    }}
                    className="block w-full text-left px-4 py-2 text-base font-medium text-foreground hover:bg-accent hover:text-accent-foreground rounded-md"
                  >
                    Sign In
                  </button>
                  <button
                    onClick={() => {
                      setAuthOpen(true);
                      setIsMenuOpen(false);
                    }}
                    className="block w-full text-left px-4 py-2 text-base font-medium text-primary-foreground bg-primary hover:bg-primary/90 rounded-md"
                  >
                    Get Started
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      
    </header>
  );
}
